#!/usr/bin/env python3
"""
Simple test runner that writes output to file
"""

import asyncio
import sys
import traceback
from datetime import datetime

# Write to file for debugging
def log_to_file(message):
    with open("scraper_test_log.txt", "a") as f:
        f.write(f"{datetime.now()}: {message}\n")
        f.flush()

async def main():
    try:
        log_to_file("Starting scraper test...")
        
        # Import the scraper
        from src.clean_digistore24_scraper import CleanDigistore24Scraper
        log_to_file("Imported scraper successfully")
        
        # Create scraper instance
        scraper = CleanDigistore24Scraper(db_path="data/clean_products.db")
        log_to_file("Created scraper instance")
        
        # Start browser
        log_to_file("Connecting to existing Chrome debug instance...")
        await scraper.start_browser(use_existing=True)
        log_to_file("Connected to existing browser successfully")
        
        # Navigate to marketplace
        log_to_file("Navigating to marketplace...")
        marketplace_url = "https://www.digistore24-app.com/app/en/affiliate/account/marketplace/all?autoAcceptAffiliations%5B%5D=Y"
        await scraper.page.goto(marketplace_url)
        
        # Wait and check URL
        await asyncio.sleep(3)
        current_url = scraper.page.url
        log_to_file(f"Current URL: {current_url}")
        
        if "login" in current_url:
            log_to_file("Redirected to login - waiting for manual completion...")
            # Wait for manual login
            try:
                await scraper.page.wait_for_url("**/marketplace/**", timeout=120000)
                log_to_file("Login completed successfully")
            except:
                log_to_file("Login timeout")
                return
        
        # Wait for products
        log_to_file("Waiting for products to load...")
        await scraper.page.wait_for_selector('ds-marketplace-product-list-item', timeout=15000)
        log_to_file("Products loaded")
        
        # Test affiliate link extraction on first product
        log_to_file("Testing affiliate link extraction...")
        products = await scraper.page.query_selector_all('ds-marketplace-product-list-item')
        log_to_file(f"Found {len(products)} products")
        
        if products:
            first_product = products[0]
            affiliate_link = await scraper.get_affiliate_link(first_product)
            log_to_file(f"Affiliate link result: {affiliate_link}")
        
        log_to_file("Test completed successfully")
        
    except Exception as e:
        log_to_file(f"Error: {str(e)}")
        log_to_file(f"Traceback: {traceback.format_exc()}")
    
    finally:
        try:
            await scraper.close_browser()
            log_to_file("Browser closed")
        except:
            log_to_file("Error closing browser")

if __name__ == "__main__":
    # Clear log file
    with open("scraper_test_log.txt", "w") as f:
        f.write(f"Test started at {datetime.now()}\n")
    
    asyncio.run(main())
