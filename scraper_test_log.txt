Test started at 2025-08-29 16:50:47.845441
2025-08-29 16:50:47.845743: Starting scraper test...
2025-08-29 16:50:47.864378: Imported scraper successfully
2025-08-29 16:50:47.865939: Created scraper instance
2025-08-29 16:50:47.866005: Connecting to existing Chrome debug instance...
2025-08-29 16:50:48.343675: Connected to existing browser successfully
2025-08-29 16:50:48.343866: Navigating to marketplace...
2025-08-29 16:50:48.830980: Error: Page.goto: Target page, context or browser has been closed
Call log:
  - navigating to "https://www.digistore24-app.com/app/en/affiliate/account/marketplace/all?autoAcceptAffiliations%5B%5D=Y", waiting until "load"

2025-08-29 16:50:48.831265: Traceback: Traceback (most recent call last):
  File "/Users/<USER>/Documents/pintrest_affiliate/run_scraper_test.py", line 37, in main
    await scraper.page.goto(marketplace_url)
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/playwright/async_api/_generated.py", line 8992, in goto
    await self._impl_obj.goto(
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/playwright/_impl/_page.py", line 556, in goto
    return await self._main_frame.goto(**locals_to_params(locals()))
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/playwright/_impl/_frame.py", line 153, in goto
    await self._channel.send(
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/playwright/_impl/_connection.py", line 69, in send
    return await self._connection.wrap_api_call(
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/playwright/_impl/_connection.py", line 558, in wrap_api_call
    raise rewrite_error(error, f"{parsed_st['apiName']}: {error}") from None
playwright._impl._errors.TargetClosedError: Page.goto: Target page, context or browser has been closed
Call log:
  - navigating to "https://www.digistore24-app.com/app/en/affiliate/account/marketplace/all?autoAcceptAffiliations%5B%5D=Y", waiting until "load"


2025-08-29 16:50:48.837757: Browser closed
