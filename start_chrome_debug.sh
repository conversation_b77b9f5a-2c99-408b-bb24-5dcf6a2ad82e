#!/bin/bash

# Start Chrome with Remote Debugging for Pinterest Automation
# This script starts Chrome with remote debugging enabled so our automation can connect to it

echo "🚀 Starting Chrome with Remote Debugging for Pinterest Automation"
echo "================================================================="

# Check if Chrome is already running with remote debugging
if lsof -i :9222 > /dev/null 2>&1; then
    echo "⚠️  Chrome is already running on port 9222"
    echo "💡 You can either:"
    echo "   1. Close Chrome and run this script again"
    echo "   2. Or just use the existing Chrome instance"
    echo ""
    read -p "🤔 Do you want to kill existing Chrome and restart? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        echo "🔄 Killing existing Chrome processes..."
        pkill -f "Google Chrome" || true
        pkill -f "chrome" || true
        sleep 2
    else
        echo "✅ Using existing Chrome instance"
        echo "💡 Make sure you're logged into Pinterest in that Chrome window"
        exit 0
    fi
fi

# Find Chrome executable
CHROME_PATH=""

# Common Chrome paths on macOS
if [[ "$OSTYPE" == "darwin"* ]]; then
    if [ -f "/Applications/Google Chrome.app/Contents/MacOS/Google Chrome" ]; then
        CHROME_PATH="/Applications/Google Chrome.app/Contents/MacOS/Google Chrome"
    elif [ -f "/Applications/Chrome.app/Contents/MacOS/Chrome" ]; then
        CHROME_PATH="/Applications/Chrome.app/Contents/MacOS/Chrome"
    fi
# Common Chrome paths on Linux
elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
    if command -v google-chrome &> /dev/null; then
        CHROME_PATH="google-chrome"
    elif command -v google-chrome-stable &> /dev/null; then
        CHROME_PATH="google-chrome-stable"
    elif command -v chromium-browser &> /dev/null; then
        CHROME_PATH="chromium-browser"
    elif command -v chromium &> /dev/null; then
        CHROME_PATH="chromium"
    fi
fi

if [ -z "$CHROME_PATH" ]; then
    echo "❌ Chrome not found!"
    echo "💡 Please install Google Chrome or update the path in this script"
    exit 1
fi

echo "✅ Found Chrome at: $CHROME_PATH"

# Create debug directory and copy default profile for saved logins
USER_DATA_DIR="$HOME/.pinterest_chrome_debug"
DEFAULT_CHROME_DIR="$HOME/Library/Application Support/Google/Chrome"

echo "📁 Setting up debug directory with your saved logins..."

# Create debug directory
mkdir -p "$USER_DATA_DIR"

# Copy essential profile data if it doesn't exist
if [ ! -f "$USER_DATA_DIR/Default/Preferences" ] && [ -d "$DEFAULT_CHROME_DIR/Default" ]; then
    echo "🔄 Copying your Chrome profile for saved logins..."
    cp -r "$DEFAULT_CHROME_DIR/Default" "$USER_DATA_DIR/" 2>/dev/null || true
    echo "✅ Profile copied successfully"
fi

echo "📁 Using debug directory: $USER_DATA_DIR"

# Chrome flags for remote debugging
CHROME_FLAGS=(
    "--remote-debugging-port=9222"
    "--remote-debugging-address=0.0.0.0"
    "--user-data-dir=$USER_DATA_DIR"
    "--no-first-run"
    "--no-default-browser-check"
    "--disable-web-security"
    "--disable-features=VizDisplayCompositor"
    "--disable-background-timer-throttling"
    "--disable-backgrounding-occluded-windows"
    "--disable-renderer-backgrounding"
)

echo "🌐 Starting Chrome with remote debugging..."
echo "💡 Chrome will open with debugging enabled on port 9222"
echo ""

# Start Chrome
"$CHROME_PATH" "${CHROME_FLAGS[@]}" "https://www.pinterest.com" &

CHROME_PID=$!

echo "✅ Chrome started with PID: $CHROME_PID"
echo "🌐 Remote debugging available at: http://localhost:9222"
echo ""
echo "📋 Next Steps:"
echo "1. 🔐 Log into Pinterest in the Chrome window that just opened"
echo "2. 🧪 Test the automation with: python3 run_chrome_automation.py --limit 1"
echo "3. 🚀 Run full automation with: python3 run_chrome_automation.py --limit 3"
echo ""
echo "💡 Tips:"
echo "- Keep this Chrome window open while running automation"
echo "- The automation will control this Chrome instance"
echo "- You can watch the automation work in real-time"
echo ""
echo "⏹️  To stop Chrome debugging, press Ctrl+C or close this terminal"

# Wait for Chrome to start
sleep 3

# Check if Chrome is running with debugging
if lsof -i :9222 > /dev/null 2>&1; then
    echo "✅ Chrome remote debugging is active on port 9222"
    echo "🎯 Ready for Pinterest automation!"
else
    echo "❌ Chrome remote debugging failed to start"
    echo "💡 Try running Chrome manually with:"
    echo "   $CHROME_PATH --remote-debugging-port=9222"
fi

# Keep script running to show Chrome is active
echo ""
echo "🔄 Chrome is running... Press Ctrl+C to stop"

# Wait for user to stop
trap 'echo ""; echo "🛑 Stopping Chrome..."; kill $CHROME_PID 2>/dev/null; exit 0' INT

# Keep the script running
while kill -0 $CHROME_PID 2>/dev/null; do
    sleep 5
done

echo "🔄 Chrome process ended"
