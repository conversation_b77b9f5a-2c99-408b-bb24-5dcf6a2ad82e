#!/usr/bin/env python3
"""
Clean Digistore24 Scraper - Focused and Simple
Scrapes products with proper DOM-clicking pagination
"""

import asyncio
import logging
import sqlite3
from datetime import datetime
from typing import Dict, List, Optional, Callable
from playwright.async_api import async_playwright, Page
import re
import json

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class CleanDigistore24Scraper:
    def __init__(self, db_path: str = "data/products.db"):
        self.db_path = db_path
        self.page: Optional[Page] = None
        self.browser = None
        self.context = None
        self.next_button_coordinates = None  # Store coordinates for reliable clicking

        # Initialize database
        self._init_database()
    
    def _init_database(self):
        """Initialize SQLite database with clean schema"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Products table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS products (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                title TEXT NOT NULL UNIQUE,
                price TEXT,
                commission TEXT,
                vendor TEXT,
                conversion_rate TEXT,
                earnings_per_sale TEXT,
                category TEXT,
                affiliate_link TEXT,
                description TEXT,
                target_audience TEXT,
                benefits TEXT,
                date_added TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                last_posted TIMESTAMP,
                post_count INTEGER DEFAULT 0,
                is_active BOOLEAN DEFAULT 1
            )
        ''')
        
        # Pinterest content table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS pinterest_content (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                product_id INTEGER,
                pinterest_title TEXT,
                pinterest_description TEXT,
                hashtags TEXT,
                board_name TEXT,
                marketing_angle TEXT,
                is_used BOOLEAN DEFAULT 0,
                date_created TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (product_id) REFERENCES products (id)
            )
        ''')
        
        conn.commit()
        conn.close()
        logger.info("✅ Database initialized")
    
    async def start_browser(self, use_existing: bool = True):
        """Start Playwright browser or connect to existing Chrome"""
        self.playwright = await async_playwright().start()

        if use_existing:
            try:
                # Try to connect to existing Chrome instance
                logger.info("🔗 Attempting to connect to existing Chrome instance...")
                self.browser = await self.playwright.chromium.connect_over_cdp("http://localhost:9222")

                # Get existing page or create new one
                contexts = self.browser.contexts
                if contexts:
                    pages = contexts[0].pages
                    if pages:
                        self.page = pages[0]
                        logger.info("✅ Connected to existing Chrome tab")
                    else:
                        self.page = await contexts[0].new_page()
                        logger.info("✅ Created new tab in existing Chrome")
                else:
                    self.context = await self.browser.new_context()
                    self.page = await self.context.new_page()
                    logger.info("✅ Created new context in existing Chrome")

            except Exception as e:
                logger.info(f"⚠️ Could not connect to existing Chrome: {e}")
                logger.info("🚀 Starting new browser instance...")
                use_existing = False

        if not use_existing:
            # Start new browser instance
            self.browser = await self.playwright.chromium.launch(headless=False)
            self.context = await self.browser.new_context()
            self.page = await self.context.new_page()
            logger.info("✅ New browser started")
    
    async def close_browser(self):
        """Close browser and cleanup"""
        try:
            if hasattr(self, 'context') and self.context:
                await self.context.close()
            elif self.browser and hasattr(self.browser, 'close'):
                # Only close if it's our own browser instance, not a connected one
                if not hasattr(self.browser, 'contexts') or not self.browser.contexts:
                    await self.browser.close()
        except Exception as e:
            logger.debug(f"Browser close warning: {e}")

        if self.playwright:
            await self.playwright.stop()
        logger.info("✅ Browser closed")
    
    async def login(self, email: str, password: str) -> bool:
        """Login to Digistore24"""
        try:
            logger.info("🔐 Logging into Digistore24...")
            await self.page.goto("https://www.digistore24.com/login")

            # Check if already logged in
            current_url = self.page.url
            if "dashboard" in current_url or "app/en" in current_url:
                logger.info("✅ Already logged in")
                return True

            # Handle cookies popup if present
            try:
                logger.debug("🍪 Checking for cookies popup...")
                cookies_button = await self.page.wait_for_selector('button:has-text("Accept"), button:has-text("OK"), button:has-text("Agree"), [data-test="accept-cookies"], .cookie-accept', timeout=3000)
                if cookies_button:
                    logger.debug("🍪 Accepting cookies...")
                    await cookies_button.click()
                    await asyncio.sleep(1)
            except:
                logger.debug("🍪 No cookies popup found")

            # Wait for login form to load in iframe
            await self.page.wait_for_selector('iframe', timeout=10000)
            iframe = await self.page.query_selector('iframe')
            iframe_content = await iframe.content_frame()

            # Fill email field (using exact working selector)
            await iframe_content.wait_for_selector('textbox[aria-label="Email"]', timeout=10000)
            await iframe_content.fill('textbox[aria-label="Email"]', email)

            # Fill password field (using exact working selector)
            await iframe_content.fill('textbox[aria-label="Password"]', password)

            # Click login button (using exact working selector)
            await iframe_content.click('button:has-text("Login")')

            # Wait for redirect to dashboard (or handle OTP)
            try:
                await self.page.wait_for_url("**/dashboard**", timeout=30000)
                logger.info("✅ Login successful")
                return True
            except:
                # Check if we're on OTP page or already logged in
                current_url = self.page.url
                if "one_time_password" in current_url:
                    logger.warning("🔐 One-time password required - please complete login manually")
                    logger.info("⏳ Waiting for manual login completion...")
                    await self.page.wait_for_url("**/dashboard**", timeout=60000)
                    logger.info("✅ Manual login completed")
                    return True
                elif "dashboard" in current_url or "app/en" in current_url:
                    logger.info("✅ Login successful (already on dashboard)")
                    return True
                else:
                    raise Exception("Login failed - unknown redirect")

        except Exception as e:
            # Check if we ended up logged in despite the error
            try:
                current_url = self.page.url
                if "dashboard" in current_url or "app/en" in current_url:
                    logger.info("✅ Login successful (despite error)")
                    return True
            except:
                pass

            logger.error(f"❌ Login failed: {e}")
            return False

    async def navigate_directly_to_marketplace(self) -> bool:
        """Navigate directly to marketplace (assumes already logged in)"""
        try:
            logger.info("🏪 Navigating directly to marketplace...")
            marketplace_url = "https://www.digistore24-app.com/app/en/affiliate/account/marketplace/all?autoAcceptAffiliations%5B%5D=Y"
            await self.page.goto(marketplace_url)

            # Wait for products to load
            await self.page.wait_for_selector('ds-marketplace-product-list-item', timeout=15000)
            logger.info("✅ Marketplace loaded")
            return True

        except Exception as e:
            logger.error(f"❌ Failed to navigate to marketplace: {e}")
            return False
    
    async def navigate_to_marketplace(self) -> bool:
        """Navigate to affiliate marketplace"""
        try:
            logger.info("🏪 Navigating to marketplace...")
            marketplace_url = "https://www.digistore24-app.com/app/en/affiliate/account/marketplace/all?autoAcceptAffiliations%5B%5D=Y"
            await self.page.goto(marketplace_url)
            
            # Wait for products to load
            await self.page.wait_for_selector('ds-marketplace-product-list-item', timeout=10000)
            logger.info("✅ Marketplace loaded")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to navigate to marketplace: {e}")
            return False
    
    async def get_page_signature(self) -> str:
        """Get unique signature of current page content for change detection"""
        try:
            products = await self.page.query_selector_all('ds-marketplace-product-list-item')
            titles = []
            
            for product in products[:3]:  # Check first 3 products
                try:
                    title_elem = await product.query_selector('h2')
                    if title_elem:
                        title = await title_elem.text_content()
                        titles.append(title.strip())
                except:
                    continue
            
            signature = " | ".join(titles)
            logger.debug(f"Page signature: {signature[:100]}...")
            return signature
            
        except Exception as e:
            logger.debug(f"Failed to get page signature: {e}")
            return ""
    
    async def navigate_to_page(self, page_number: int) -> bool:
        """Navigate directly to a specific page number using URL manipulation

        This method bypasses all button clicking and directly modifies the URL
        to go to the target page number. For example:
        - Current: https://...marketplace/all?autoAcceptAffiliations[]=Y
        - Target:  https://...marketplace/all?autoAcceptAffiliations[]=Y&page=4
        """
        try:
            logger.info(f"🔄 Using DIRECT URL NAVIGATION to page {page_number}...")

            # Get the current URL and modify it to go to the target page
            current_url = self.page.url
            logger.info(f"📍 Current URL: {current_url}")

            # Check if URL already has page parameter
            if "page=" in current_url:
                # Replace existing page number
                import re
                new_url = re.sub(r'page=\d+', f'page={page_number}', current_url)
                logger.info(f"🔄 Replacing existing page parameter")
            else:
                # Add page parameter
                separator = "&" if "?" in current_url else "?"
                new_url = f"{current_url}{separator}page={page_number}"
                logger.info(f"➕ Adding new page parameter")

            logger.info(f"🌐 NEW URL: {new_url}")

            # Navigate to the new URL
            await self.page.goto(new_url)
            await asyncio.sleep(3)  # Wait for page to load

            # Wait for products to load
            try:
                await self.page.wait_for_selector('ds-marketplace-product-list-item', timeout=10000)
                logger.info(f"✅ Successfully navigated to page {page_number} via URL")
                return True

            except Exception as e:
                logger.warning(f"⚠️ Products didn't load on page {page_number}: {e}")
                logger.warning(f"🛑 This likely means page {page_number} doesn't exist - reached end of pages")
                return False

        except Exception as e:
            logger.error(f"❌ Failed to navigate to page {page_number}: {e}")
            return False

    async def click_next_page(self) -> bool:
        """Click next page using CLEAN page number clicking (no old logic)"""
        try:
            logger.info("🔄 Looking for next page arrow...")

            # DEBUG: Log all pagination elements to understand the structure
            logger.info("🔍 DEBUGGING PAGINATION STRUCTURE:")

            # First, find the pagination container
            pagination_container = await self.page.query_selector('[data-test="pagination-pages"]')
            if pagination_container:
                # Get all buttons/links in pagination
                all_pagination_elements = await pagination_container.query_selector_all('button, a')
                logger.info(f"📋 Found {len(all_pagination_elements)} pagination elements:")

                for i, element in enumerate(all_pagination_elements):
                    try:
                        text = await element.inner_text()
                        classes = await element.get_attribute('class')
                        tag = await element.evaluate('el => el.tagName')
                        logger.info(f"   Element {i+1}: {tag} text='{text}' classes='{classes}'")

                        # Check if this looks like the active/highlighted element
                        if classes and ('bg-primary' in classes or 'active' in classes):
                            logger.info(f"   ⭐ HIGHLIGHTED ELEMENT FOUND: text='{text}'")
                    except Exception as e:
                        logger.info(f"   Element {i+1}: Could not read properties - {e}")

            # Get current page and find next page button
            try:
                current_page_element = await self.page.query_selector('[data-test="pagination-pages"] .active')
                current_page_text = await current_page_element.inner_text() if current_page_element else "1"
                current_page_num = int(current_page_text) if current_page_text.isdigit() else 1
                next_page_num = current_page_num + 1

                logger.info(f"✅ Detected current page from blue highlighted button: {current_page_num}")
                logger.info(f"🔍 Looking for next page {next_page_num} (current: {current_page_num})")
                logger.info(f"🔄 Attempting to navigate to page {next_page_num} using Next button...")

                # Pagination should already be visible from main loop scrolling

                # SIMPLE APPROACH: Click the rightmost part of the pagination container
                logger.info(f"🎯 Clicking RIGHTMOST part of pagination container")

                try:
                    # Find the ds-pagination element first, then the container inside it
                    ds_pagination = await self.page.query_selector('ds-pagination')
                    if ds_pagination:
                        pagination_container = await ds_pagination.query_selector('[data-test="pagination-pages"]')
                    else:
                        # Fallback to direct selector
                        pagination_container = await self.page.query_selector('[data-test="pagination-pages"]')

                    if pagination_container:
                        # FIRST: Scroll the pagination container into view
                        logger.info("🔄 Scrolling pagination container into view...")
                        await pagination_container.scroll_into_view_if_needed()
                        await asyncio.sleep(1)  # Wait for scroll to complete

                        # Get the bounding box of the entire pagination container
                        box = await pagination_container.bounding_box()
                        if box:
                            # Click the rightmost part (Next button area)
                            # X: 5 pixels from the right edge to ensure we're inside the clickable area
                            # Y: Middle of the container vertically
                            next_button_x = box['x'] + box['width'] - 5
                            next_button_y = box['y'] + box['height'] / 2

                            logger.info(f"🔄 Clicking at RIGHTMOST coordinates ({next_button_x:.1f}, {next_button_y:.1f})")

                            # Click at the rightmost part of pagination (always the Next button)
                            await self.page.mouse.click(next_button_x, next_button_y)
                        else:
                            logger.warning("⚠️ Could not get bounding box for pagination container")
                            return False
                    else:
                        logger.warning("⚠️ Could not find pagination container")
                        return False
                except Exception as e:
                    logger.warning(f"⚠️ Failed to click pagination: {e}")
                    return False

                # Check if navigation worked - wait a bit longer for page to load
                await asyncio.sleep(2)  # Wait for page to load
                new_page_element = await self.page.query_selector('[data-test="pagination-pages"] .active')
                new_page_text = await new_page_element.inner_text() if new_page_element else "unknown"

                if new_page_text == str(next_page_num):
                    logger.info(f"✅ Successfully navigated to page {next_page_num}")
                    return True
                else:
                    logger.warning(f"⚠️ Coordinate click failed - expected page {next_page_num}, got page {new_page_text}")
                    return False
            except Exception as e:
                logger.debug(f"Page number method failed: {e}")
                return False

        except Exception as e:
            logger.error(f"❌ Failed to click next page: {e}")
            return False
    
    async def go_to_next_page_with_verification(self, timeout_seconds: int = 30) -> bool:
        """Go to next page and verify content changed - USES NEW PAGE NUMBER CLICKING"""
        try:
            # Get current page signature
            prev_signature = await self.get_page_signature()

            # Use the NEW page number clicking method (not the old click_next_page)
            if not await self.click_next_page():
                return False

            # Wait for content to change with timeout
            start_time = asyncio.get_event_loop().time()

            while (asyncio.get_event_loop().time() - start_time) < timeout_seconds:
                await asyncio.sleep(0.5)

                # Check if content changed
                new_signature = await self.get_page_signature()
                if new_signature and new_signature != prev_signature:
                    logger.info("✅ Page content changed - pagination successful")
                    return True

            logger.warning("⚠️ Page content didn't change within timeout")
            return False

        except Exception as e:
            logger.error(f"❌ Pagination verification failed: {e}")
            return False
    
    def store_product(self, product_data: Dict) -> int:
        """Store product in database"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        try:
            cursor.execute('''
                INSERT OR REPLACE INTO products (
                    title, price, commission, vendor, conversion_rate,
                    earnings_per_sale, category, affiliate_link,
                    description, target_audience, benefits, is_active
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                product_data.get('title'),
                product_data.get('price'),
                product_data.get('commission'),
                product_data.get('vendor'),
                product_data.get('conversion_rate'),
                product_data.get('earnings_per_sale'),
                product_data.get('category'),
                product_data.get('affiliate_link'),
                product_data.get('description'),
                product_data.get('target_audience'),
                product_data.get('benefits'),
                1
            ))

            product_id = cursor.lastrowid
            conn.commit()
            logger.info(f"✅ Stored product: {product_data.get('title', 'Unknown')}")
            return product_id

        except Exception as e:
            logger.error(f"❌ Failed to store product: {e}")
            return 0
        finally:
            conn.close()

    async def extract_product_data(self, container) -> Optional[Dict]:
        """Extract product data from container element"""
        try:
            container_text = await container.inner_text()

            # Extract title from h2 element
            title = "Unknown Product"
            try:
                title_elem = await container.query_selector('h2')
                if title_elem:
                    title_text = await title_elem.inner_text()
                    if title_text and title_text.strip():
                        title = title_text.strip()
            except:
                pass

            # Extract price (look for $ amounts)
            price = ""
            price_matches = re.findall(r'\$[\d,]+\.?\d*', container_text)
            if price_matches:
                price = price_matches[0]

            # Extract commission percentage
            commission = ""
            comm_matches = re.findall(r'(\d+\.?\d*%)', container_text)
            if comm_matches:
                commission = comm_matches[0]

            # Extract vendor name
            vendor = ""
            try:
                # Look for vendor info in container text
                lines = container_text.split('\n')
                for line in lines:
                    if line.strip() and not any(char in line for char in ['$', '%', '.']):
                        if len(line.strip()) < 50 and len(line.strip()) > 3:
                            vendor = line.strip()
                            break
            except:
                pass

            # Extract conversion rate (percentage with asterisk)
            conversion_rate = ""
            conv_matches = re.findall(r'(\d+\.?\d*%)\*', container_text)
            if conv_matches:
                conversion_rate = conv_matches[0]

            # Extract earnings per sale from h2 elements
            earnings_per_sale = ""
            try:
                earnings_elements = await container.query_selector_all('h2')
                for elem in earnings_elements:
                    text = await elem.inner_text()
                    if '$' in text and text != price:
                        earnings_per_sale = text.strip()
                        break
            except:
                pass

            # Determine category from title
            category = self._categorize_product(title)

            product_data = {
                'title': title,
                'price': price,
                'commission': commission,
                'vendor': vendor,
                'conversion_rate': conversion_rate,
                'earnings_per_sale': earnings_per_sale,
                'category': category,
                'description': f"High-converting {category} product with {commission} commission rate.",
                'target_audience': f"People interested in {category.replace('_', ' ')}, health and wellness enthusiasts",
                'benefits': f"High-quality {category.replace('_', ' ')} solution with proven results"
            }

            logger.info(f"📦 Extracted: {title} - {price} - {commission}")
            return product_data

        except Exception as e:
            logger.error(f"❌ Error extracting product data: {e}")
            return None

    def _categorize_product(self, title: str) -> str:
        """Categorize product based on title"""
        title_lower = title.lower()

        if any(word in title_lower for word in ['supplement', 'health', 'vitamin', 'probiotic', 'tonic']):
            return 'health_supplements'
        elif any(word in title_lower for word in ['weight', 'slim', 'diet', 'fat', 'keto']):
            return 'weight_loss'
        elif any(word in title_lower for word in ['book', 'guide', 'manual', 'course']):
            return 'digital_products'
        elif any(word in title_lower for word in ['manifestation', 'wealth', 'money', 'success']):
            return 'self_improvement'
        else:
            return 'general'

    async def get_affiliate_link(self, container) -> Optional[str]:
        """Get affiliate link by clicking Copy promolink or Promote now button"""
        try:
            # First, try Copy promolink button (direct method)
            copy_button = await container.query_selector('button:has-text("Copy promolink")')

            if copy_button:
                logger.debug("🔗 Found Copy promolink button, clicking...")
                await copy_button.click()
                await asyncio.sleep(0.5)

                # Try to get link from clipboard
                try:
                    clipboard_content = await self.page.evaluate('''
                        async () => {
                            try {
                                return await navigator.clipboard.readText();
                            } catch (e) {
                                return null;
                            }
                        }
                    ''')

                    if clipboard_content and ('digistore24.com' in clipboard_content or '.com' in clipboard_content):
                        logger.info(f"✅ Got affiliate link via Copy promolink: {clipboard_content[:50]}...")
                        return clipboard_content.strip()

                except Exception as e:
                    logger.debug(f"Clipboard read failed: {e}")

            # If no Copy promolink button, try Promote now button
            promote_button = await container.query_selector('button:has-text("Promote now")')

            if promote_button:
                logger.info("🔗 Found Promote now button, clicking...")
                await promote_button.click()
                await asyncio.sleep(0.3)  # Faster wait for response (modal or button transformation)

                # Check what happened after clicking "Promote now"
                # Option 1: Modal opened
                modal_selectors = ['dialog', '[role="dialog"]', '.modal', '[class*="modal"]']
                modal_found = False
                for selector in modal_selectors:
                    try:
                        modal = await self.page.query_selector(selector)
                        if modal:
                            logger.info("🔍 Modal opened after Promote now click")
                            modal_found = True
                            break
                    except:
                        continue

                if modal_found:
                    # Handle modal workflow
                    try:
                        # Wait for modal dialog to appear (try multiple selectors)
                        logger.debug("🔍 Waiting for modal dialog to appear...")

                        modal_selectors = [
                            'dialog',
                            '[role="dialog"]',
                            '.modal',
                            '[class*="modal"]',
                            '[class*="popup"]',
                            '[class*="overlay"]'
                        ]

                        modal = None
                        for selector in modal_selectors:
                            try:
                                modal = await self.page.wait_for_selector(selector, timeout=2000)
                                if modal:
                                    logger.debug(f"✅ Found modal with selector: {selector}")
                                    break
                            except:
                                continue

                        if not modal:
                            raise Exception("No modal found with any selector")

                        # Modal opened - extract affiliate link quickly (no verbose logging)
                        logger.debug("🔍 Modal opened, extracting affiliate link...")

                        # Extract affiliate link from disabled textbox (try multiple selectors)
                        textbox_selectors = [
                            'textbox[disabled]',
                            'input[disabled]',
                            'textbox[readonly]',
                            'input[readonly]',
                            'textbox',
                            'input[type="text"]'
                        ]

                        affiliate_link = None
                        for selector in textbox_selectors:
                            try:
                                textbox = await modal.query_selector(selector)
                                if textbox:
                                    # Try multiple ways to get the value
                                    link_value = await textbox.get_attribute('value')
                                    if not link_value or link_value == 'None':
                                        # Try getting the input value property
                                        link_value = await textbox.evaluate('el => el.value')
                                    if not link_value or link_value == 'None':
                                        # Try getting inner text
                                        link_value = await textbox.inner_text()

                                    logger.info(f"🔍 Textbox value with {selector}: '{link_value}'")

                                    if link_value and link_value != 'None' and ('http' in link_value or '.com' in link_value):
                                        affiliate_link = link_value
                                        logger.info(f"✅ Found link with selector {selector}: {affiliate_link[:50]}...")
                                        break
                            except Exception as e:
                                logger.debug(f"Textbox selector {selector} failed: {e}")
                                continue

                        if affiliate_link:
                            logger.info(f"✅ Got affiliate link via Promote now modal: {affiliate_link[:50]}...")

                            # Click the copy button in the modal to copy to clipboard
                            try:
                                logger.info("🔍 Looking for copy button in modal...")

                                # Try multiple selectors for the copy button (Button 2 based on debug output)
                                copy_selectors = [
                                    'button:nth-child(2)',  # Second button (the copy button we identified)
                                    'button.mat-mdc-tooltip-trigger',  # Button with tooltip trigger class
                                    'button[class*="tooltip-trigger"]',  # Button with tooltip in class
                                    'button[class*="w-10"]',  # Button with w-10 class (width class)
                                    'button:has(img)',  # Button containing an image
                                    'button img',       # Image inside button (click the image)
                                    'button:not(:last-child)',  # Not the last button (usually Confirm)
                                    'button:not([class*="Confirm"])',  # Not the confirm button
                                    'button'  # Any button (fallback)
                                ]

                                copy_button = None
                                for i, selector in enumerate(copy_selectors):
                                    try:
                                        copy_button = await modal.query_selector(selector)
                                        if copy_button:
                                            logger.info(f"✅ Found copy button with selector #{i+1}: {selector}")
                                            break
                                    except Exception as e:
                                        logger.debug(f"Selector {selector} failed: {e}")
                                        continue

                                if copy_button:
                                    logger.info("🔄 Clicking copy button in modal...")
                                    await copy_button.click()
                                    await asyncio.sleep(0.1)  # Wait for copy operation
                                    logger.info("✅ Copy button clicked successfully")
                                else:
                                    logger.warning("⚠️ Could not find any copy button in modal")
                                    # Try clicking the first button as fallback
                                    buttons = await modal.query_selector_all('button')
                                    if buttons:
                                        logger.info(f"🔄 Fallback: clicking first of {len(buttons)} buttons...")
                                        await buttons[0].click()
                                        await asyncio.sleep(0.1)
                                        logger.info("✅ Fallback button clicked")

                            except Exception as e:
                                logger.error(f"Copy button click failed: {e}")

                            # Close modal with Escape key (fast method)
                            await self.page.keyboard.press('Escape')
                            await asyncio.sleep(0.05)  # Brief wait for modal to close

                            return affiliate_link.strip()

                            # If textbox extraction failed, log and close modal
                            logger.debug("⚠️ Could not extract affiliate link from textbox")

                            # Close modal even if extraction failed
                            await self.page.keyboard.press('Escape')
                            await asyncio.sleep(0.05)

                    except Exception as e:
                        logger.info(f"Modal handling failed: {e}")
                        # Try to close any open modal
                        try:
                            await self.page.keyboard.press('Escape')
                            await asyncio.sleep(0.05)
                        except:
                            pass

                # Option 2: Button transformed to "Copy promolink" (if no modal was found)
                if not modal_found:
                    logger.info("🔍 No modal found, checking if button transformed to Copy promolink...")
                    copy_button = await container.query_selector('button:has-text("Copy promolink")')
                    if copy_button:
                        logger.info("🔄 Found Copy promolink button after Promote now click - clicking...")
                        await copy_button.click()
                        await asyncio.sleep(0.5)  # Wait for clipboard copy

                        # Extract from clipboard
                        try:
                            clipboard_content = await self.page.evaluate('''
                                async () => {
                                    try {
                                        return await navigator.clipboard.readText();
                                    } catch (e) {
                                        return null;
                                    }
                                }
                            ''')

                            if clipboard_content and ('http' in clipboard_content or '.com' in clipboard_content):
                                logger.info(f"✅ Got affiliate link via transformed Copy promolink: {clipboard_content[:50]}...")
                                return clipboard_content.strip()
                        except Exception as e:
                            logger.debug(f"Clipboard read after transformation failed: {e}")
                    else:
                        logger.debug("⚠️ No Copy promolink button found after Promote now click")

            # If neither button type worked
            logger.info("⏭️ No affiliate link buttons found - skipping product")
            return None

        except Exception as e:
            logger.debug(f"Affiliate link extraction failed: {e}")
            # Ensure any open modal is closed
            try:
                await self.page.keyboard.press('Escape')
                await asyncio.sleep(0.05)
            except:
                pass
            return None

    async def scrape_products(self, max_products: int = 100, max_pages: int = 10,
                            min_commission: float = 30.0, min_conversion: float = 5.0,
                            callback: Optional[Callable] = None) -> List[Dict]:
        """
        Scrape products from Digistore24 marketplace

        Args:
            max_products: Maximum number of products to scrape
            max_pages: Maximum number of pages to scrape
            min_commission: Minimum commission percentage
            min_conversion: Minimum conversion rate percentage
            callback: Optional callback function for each product

        Returns:
            List of scraped product data
        """
        products = []
        collected = 0
        page_num = 1
        seen_titles = set()

        logger.info(f"🚀 Starting scrape: max_products={max_products}, max_pages={max_pages}")
        logger.info(f"📊 Filters: min_commission={min_commission}%, min_conversion={min_conversion}%")

        try:
            while page_num <= max_pages and collected < max_products:
                logger.info(f"📄 Scraping page {page_num}...")

                # Wait for products to load
                await self.page.wait_for_selector('ds-marketplace-product-list-item', timeout=10000)

                # Get all product containers on current page
                containers = await self.page.query_selector_all('ds-marketplace-product-list-item')
                logger.info(f"📦 Found {len(containers)} products on page {page_num}")

                page_products = 0

                for i, container in enumerate(containers):
                    if collected >= max_products:
                        break

                    try:
                        # ALWAYS scroll past each product container to reach proper end position
                        await container.scroll_into_view_if_needed()
                        await asyncio.sleep(0.1)  # Brief pause for smooth scrolling

                        # Check if product requires manual promote request (skip these)
                        container_text = await container.inner_text()
                        if "Send promote request" in container_text:
                            logger.debug(f"⏭️ Skipping product {i+1} (requires promote request)")
                            continue

                        # Extract product data
                        product_data = await self.extract_product_data(container)
                        if not product_data or not product_data.get('title'):
                            continue

                        title = product_data['title']

                        # Skip duplicates
                        if title in seen_titles:
                            logger.debug(f"⏭️ Skipping duplicate: {title[:50]}...")
                            continue

                        seen_titles.add(title)

                        # Filter by commission and conversion rate
                        commission = self._parse_percentage(product_data.get('commission', '0'))
                        conversion = self._parse_percentage(product_data.get('conversion_rate', '0'))

                        if commission >= min_commission and conversion >= min_conversion:
                            # Get affiliate link
                            affiliate_link = await self.get_affiliate_link(container)

                            if affiliate_link:
                                product_data['affiliate_link'] = affiliate_link

                                # Store in database
                                product_id = self.store_product(product_data)
                                product_data['id'] = product_id

                                products.append(product_data)
                                collected += 1
                                page_products += 1

                                logger.info(f"✅ Scraped product {collected}: {title[:50]}...")

                                # Call callback if provided
                                if callback:
                                    await callback(product_data)
                            else:
                                logger.debug(f"⏭️ Skipping product (no affiliate link): {title[:50]}...")
                        else:
                            logger.debug(f"⏭️ Skipping product (low metrics): {title[:50]}... "
                                       f"Commission: {commission}%, Conversion: {conversion}%")

                    except Exception as e:
                        logger.debug(f"Error processing product {i+1}: {e}")
                        continue

                logger.info(f"📊 Page {page_num} complete: {page_products} products scraped")

                # SCROLL TO BOTTOM after processing all products to reveal pagination
                logger.info("🔄 Scrolling to bottom of page to reveal pagination...")
                await self.page.evaluate("window.scrollTo(0, document.body.scrollHeight)")
                await asyncio.sleep(2)  # Wait for scroll to complete and pagination to be visible

                # Try to go to next page using BUTTON CLICKING (as user specified)
                if page_num < max_pages and collected < max_products:
                    logger.info(f"🔄 Moving to page {page_num + 1} using Next button...")

                    if await self.go_to_next_page_with_verification():
                        page_num += 1
                        logger.info(f"✅ Successfully moved to page {page_num}")
                    else:
                        logger.warning("🛑 Could not move to next page - stopping")
                        break
                else:
                    break

            logger.info(f"🎉 Scraping complete! Collected {collected} products from {page_num} pages")
            return products

        except Exception as e:
            logger.error(f"❌ Scraping failed: {e}")
            return products

    def _parse_percentage(self, percentage_str: str) -> float:
        """Parse percentage string to float"""
        try:
            if not percentage_str:
                return 0.0
            # Remove % and convert to float
            clean_str = percentage_str.replace('%', '').strip()
            return float(clean_str)
        except:
            return 0.0
